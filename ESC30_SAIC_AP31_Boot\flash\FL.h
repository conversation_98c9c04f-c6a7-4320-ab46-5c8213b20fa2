/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <FL.h>
 *  @brief      <Macros,Types defines and function decalrations for Flash Driver 
 *              Module>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR>
 *  @date       <2013-09-13>
 */
/*============================================================================*/

#ifndef FLASHLOADER_H_
#define FLASHLOADER_H_

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 *
 *  V1.1    20130913    ccl        update
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/

#include "Platform_Types.h"
#include "Std_Types.h"
#include "FL_Cfg.h"
#include "Secure_Types.h"
#include <stdio.h>
/*=======[M A C R O S]========================================================*/
/*****************************************************************************
*   Global Define Definitions
*****************************************************************************/
#define FBL_FALSE            0x00u
#define FBL_TRUE             0x01u
/* FL module execute result */
#define FL_OK               0x00u
#define FL_FAILED           0x01u
#define FL_ERR_SEQUENCE     0x02u
#define FL_NO_FINGERPRINT   0x03u
#define FL_NO_FLASHDRIVER   0x04u
#define FL_ERR_ADDR_LENGTH  0x05u
#define FL_INVALID_DATA     0x06u
#define FL_MODULEID_UNMATCH 0x07u
#define FL_OUT_OF_RANGE     0x08u
#define FL_WRITEMOREONETIME 0xFEu
/** Value indicate an external programming request. */
#define FL_EXT_PROG_REQUEST_RECEIVED    0x01u
#define FL_EXT_PROG_REQUEST82_RECEIVED    0x3Au
/** Value indicate an update of the application software. */
#define FL_APPL_UPDATED 0xD5u

/** Value indicate default session request from prog. of the bootloader software. */
#define FL_BOOT_DEFAULT_FROM_PROG       0x01u

#define FBL_SECURE_NRC_OFFSET 0x10u
#define FBL_SECURE_MODULEID_UNMATCH (SECURE_MODULEID_UNMATCH + FBL_SECURE_NRC_OFFSET)
#define FBL_SECURE_CERT_VERIF_FAIL  (SECURE_CERT_VERIF_FAIL  + FBL_SECURE_NRC_OFFSET)
#define FBL_SECURE_SIGN_VERIF_FAIL  (SECURE_SIGN_VERIF_FAIL  + FBL_SECURE_NRC_OFFSET)
#define FBL_SECURE_VERSION_TOOLOW   (SECURE_VERSION_TOOLOW   + FBL_SECURE_NRC_OFFSET)
#define FBL_SECURE_OUT_OF_RANGE     (SECURE_OUT_OF_RANGE     + FBL_SECURE_NRC_OFFSET)
/*=======[T Y P E   D E F I N I T I O N S]====================================*/
/** Standard return type for callback routines. */
typedef uint8 FL_ResultType;


typedef enum
{
	INTERNAL_FLS,
	EXTERNAL_FLS
}FL_FlashType;

typedef enum
{
	NO_CRC,
	LAST_ADDR,
	HEAD_ADDR
}FL_CrcAddrType;

/* 
 ** needed in the interface between flashloader runtime environment and security
 ** module.
 */
typedef struct
{
    /* block start global address */
    const uint32 address;

    /* block length */
    const uint32 length;
    
    const FL_FlashType flashtype;

    const FL_CrcAddrType crcaddrtype;
    const uint32 crcaddress;
    const boolean isvital;
    /* maxmun program cycle */
    const uint32 maxProgAttempt;
    const uint8 moduleid;
} FL_BlockDescriptorType;

/** Segment list infomation of the block */
typedef struct
{
    /* Start global address of the segment in flash */
    uint32 address;
    
    /* Length of the segment */
    uint32 length;

} FL_SegmentInfoType;

/** Needed in interface to the security module. */
typedef struct
{
    /* number of segment */
	uint16 nrOfSegments;
	boolean blockChecked;
    /* segments infomation */
	 FL_SegmentInfoType segmentInfo[FL_MAX_SEGMENTS];
} FL_SegmentListType;

/** Each logical block infomation in EEPROM. */
typedef struct
{
    boolean blkValid;
    
    uint16 blkProgAttempt;
    
    uint32 blkChecksum;
    
    uint8 fingerPrint[FL_FINGER_PRINT_LENGTH];

} FL_blockInfoType;

/** Totol block infomation in EEPROM. */
typedef struct
{
    FL_blockInfoType blockInfo[FL_NUM_LOGICAL_BLOCKS ];
    
    boolean isAppCalCpb;
    boolean isAppFblCpb;
    /* NVM infomation checksum */
    uint32 infoChecksum;    
} FL_NvmInfoType;
extern uint8 Fl_crc;
/*=======[E X T E R N A L   D A T A]==========================================*/
/* infomation witch shall be programmed to EEPROM */
#if(FLS_USED == STD_ON)
extern const uint8 g_u1_FlashDriverBin[FLASH_DRIVER_SIZE];
#endif
extern FL_NvmInfoType FL_NvmInfo;

/* logical block infomation that configered by user */
extern uint8 FL_Header_Buffer[640u];
extern const FL_BlockDescriptorType FL_BlkInfo[FL_NUM_LOGICAL_BLOCKS ];
extern uint8 CurrentProgrammingBlock;
extern uint32 CurrentErasingAddress;
extern uint32 CurrentErasingLength;
/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/

extern void FL_InitState(void);
extern FL_ResultType EEP_WriteDIDF110(const uint8 *data,const uint16 length);
extern FL_ResultType EEP_WriteDIDF187(const uint8 *data,const uint16 length);
extern uint32 FL_ReadMemory(uint32 address,
    uint32 length,
    uint8* data);
extern uint8 FL_CheckProgPreCondition(uint8 * conditions);

extern FL_ResultType FL_WriteFingerPrint(const uint8 *data,
    const uint16 length);

extern FL_ResultType FL_CheckModuleId(uint8 moduleId, uint8* locationInfo, uint8* idxRet);

extern FL_ResultType FL_CheckSumRoutine(const boolean isHash);
extern FL_ResultType FL_CheckCPBRoutine(void);
extern FL_ResultType FBL_IntegrityCheck(void);
extern FL_ResultType FL_EraseRoutine(const uint8 blockIndex);

extern FL_ResultType FL_DownloadRequestValid(const uint32 startAdd,
    const uint32 length);

extern FL_ResultType FL_FlashProgramRegion(const uint32 destAddr,
    const uint8 *sourceBuff,
    const uint32 length);

extern void FL_SetExitTransferStep(void);

extern void FL_SetHeadBlockErased(void);

extern FL_ResultType FL_ExitTransferData(void);

extern boolean FL_ServiceFinished(FL_ResultType *error);

extern void FL_MainFunction(void);
extern FL_ResultType FL_CheckSumFor37(uint16* crc);
extern FL_ResultType FBL_CheckSumFor37(uint16 * crcValPtr);
extern void SetS37JobStatusBusy(void);
extern void GetS37JobStatusResult(FL_ResultType status);
extern uint8 FL_CheckProgramIntegrity(void);
extern FL_ResultType FL_CheckProgramDependencies(uint8* errorvalue);
extern FL_ResultType FL_CheckSWVerification(uint8* checkdata);
extern uint16 FL_CheckProgramCounter(void);
extern FL_ResultType FL_SignVerifFlags(void);
extern void FL_SetRequestStep(void);
extern void FL_SetChecksumStep(void);
extern void UpdateSecurityErrorFlag(void);
extern uint8 FL_GetSecErrFlag(void);
#endif/* endof FL_H */

/*=======[E N D   O F   F I L E]==============================================*/

