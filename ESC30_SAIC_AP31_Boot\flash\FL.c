/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <FL.c>
 *  @brief      <His Flash Loader >
 *
 *  < The Code process checksum,erase and program for bootloader project>
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR>
 *  @date       <2013-09-13>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 *
 *  V1.1    20130913    ccl        update
 *  V1.2    20190520    锟斤拷锟斤拷锟斤拷               锟斤拷锟斤拷DID
 * AFFC锟斤拷AFFD锟斤拷AFFE锟斤拷AFFF锟侥讹拷锟斤拷锟斤拷
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "FL.h"
#include "Appl.h"
#include "Ext_Fls.h"
#include "Fls.h"
#include "ICM_Bootloader_Version.h"
#include "Mcal_Compiler.h"
#include "Secure.h"
#include "Vss.h"

// #include "Wdg.h"
// #include "Nvm_Did_Cfg.h"
#include "eeprom_Cfg.h"
/*=======[M A C R O S]========================================================*/
#define BL_USE_COMBINATION_SWITCH STD_OFF

/** maxmun length of program buffer */
#define FL_PROGRAM_SIZE (0x100uL)
#define SYS_SUP_LENGTH 6u
#define HW_VER_LENGTH 6u
#define SW_VER_LENGTH 6u
#define MD_VER_LENGTH 6u
#define SAIC_VER_LENGTH 6u

#define VIN_VER_LENGTH 17u
#define VEH_VER_LENGTH 20u

/*=======[T Y P E   D E F I N I T I O N S]====================================*/
/** flashloader job status */
typedef enum {
  FL_JOB_IDLE,

  FL_JOB_ERASING,

  FL_JOB_PROGRAMMING,

  FL_JOB_CHECKING, // 锟斤拷锟斤拷锟斤拷校锟斤拷
  FL_JOB_S37CHECKING,
  FL_JOB_COMPATIBLE,

} FL_ActiveJobType;

/** flashloader download step */
typedef enum {
  FL_REQUEST_STEP,

  FL_HEADER_TRASNFER_STEP,

  FL_HEADER_EXIT_TRANSFER_STEP,

  FL_TRANSFER_STEP,

  FL_EXIT_TRANSFER_STEP,

  FL_CHECKSUM_STEP

} FL_DownloadStepType;

/** flashloader status infomation */
typedef struct {
  /* flag if fingerprint has written */
  boolean fingerPrintWritten;

  /* flag if fingerprint buffer */
  uint8 fingerPrint[FL_FINGER_PRINT_LENGTH];

  /* flag if flash driver has downloaded */
  boolean flDrvDownloaded;

  /* error code for flash active job */
  FL_ResultType errorCode;

  /* flag if current block is erased */
  boolean blockErased;

  /* current process block index */
  uint8 blockIndex;

  /* current procees start address */
  uint32 startAddr;

  /* current procees length */
  uint32 length;

  /* current procees buffer point,point to buffer supplied from DCM */
  const uint8 *dataBuff;

  /* segment list of current procees block */
  FL_SegmentListType segmentList[FL_NUM_LOGICAL_BLOCKS];

  /* flashloader download step */
  FL_DownloadStepType downloadStep;

  /* current job status */
  FL_ActiveJobType activeJob;

} FL_DownloadStateType;

/*handle the two segment in one page*/
typedef struct {
  /* current procees start address */
  uint32 remainAddr;

  /* current procees length */
  uint32 remainLength;
} FL_RemainDataType;

STATIC uint8 FBL_HeaderType;
STATIC uint8 FBL_DownloadFlags[1];
STATIC uint8 FBL_SignVerifFlags[1];
uint8 fblFileIndex = 0;
extern uint8 SecurityErrorFlag;
/*=======[E X T E R N A L   D A T A]==========================================*/
/** NVM infomation witch include bootloader infomation,read from EEPROM */
FL_NvmInfoType FL_NvmInfo;
/*=======[I N T E R N A L   D A T A]==========================================*/
/*save the data which not aligned*/
STATIC FL_RemainDataType FL_RemainDataStruct;
/** flashloader status infomation */
STATIC FL_DownloadStateType FldownloadStatus;
/** flashloader program buffer */
STATIC uint8 FlProgramData[FL_PROGRAM_SIZE];
/** flashloader program length */
STATIC uint32 FlProgramLength;
/** flash driver API input parameter */
STATIC tFlashParam flashParamInfo = {
    FLASH_DRIVER_VERSION_PATCH,
    FLASH_DRIVER_VERSION_MINOR,
    FLASH_DRIVER_VERSION_MAJOR,
    0x00u,
    kFlashOk,
    0x0000u,
    0x00000000uL,
    0x00000000uL,
    NULL_PTR,
    &Appl_UpdateTriggerCondition,
};
STATIC boolean FlIntegrityChkIsHash = TRUE;
/* ECU Bootloader Version Number Data Identifier, ASCII code of BL:x.y.z*/
// const uint8 BLVer[MD_VER_LENGTH] = {0x42u, 0x4Cu, 0x3Au, 0x61u, 0x2Eu,
// 0x31u};
/*
 #pragma section ".sersys_CODE" aw
 const uint8 DIDTemp[DID_BYTE_LENGTH] = {	0xF0u, 0x00u, 0x00u, 0x00u,
0x00u, 0x00u, 0x00u, 0x00u, 0xF1u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0x00u, 0x00u,
0x00u, 0xF2u, 0xF2u, 0xFFu, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0xF3u, 0xF2u,
0xF3u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu,
0xFFu, 0xFFu, 0xFFu, 0xF4u, 0xF2u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
                                                                                        0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
                                                                                        0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
                                                                                        0xF5u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0x00u, 0x00u, 0x00u,
                                                                                        0xF6u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0x00u, 0x00u, 0x00u};

#pragma section
*/
#if (STD_ON == BL_USE_COMBINATION_SWITCH)
/*Pflash and Dflash all used.*/
#pragma section ".BlInf_CODE" aw
const uint8 BL_Information[FL_NUM_LOGICAL_BLOCKS * 20 + 4] = {
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xEF, 0x7E, 0x29, 0xFC};
#pragma section
#else
/*just have DFlash .*/
#pragma section farrom = "FlsCheckData"
const uint8 BL_Information[FL_NUM_LOGICAL_BLOCKS * 20 + 8] = {0x00};
#pragma section farrom restore
#endif
extern uint16 lastcounter;
uint8 addblkProgAttempt = TRUE;
uint8 appblkIntDefault = FALSE;
uint8 appblkCpbDefault = FALSE;
#pragma section farrom "fbl_cpb_data"
const char boot_cpb_data[4] = {0x00, 0x00, 0x00, 0x00};
#pragma section farrom restore

/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
STATIC FL_ResultType FL_Erasing(void);
STATIC FL_ResultType FL_CheckDownloadSegment(void);
STATIC FL_ResultType FL_DownloadRemainData(void);
STATIC FL_ResultType FL_HandleRemainData(void);
STATIC FL_ResultType FL_ProgrammingData(void);
STATIC FL_ResultType FL_Programming(void);
#if (FL_USE_GAP_FILL == STD_ON)
STATIC FL_ResultType FL_FillGap(void);
#endif

STATIC FL_ResultType FL_CheckSuming(void);
STATIC FL_ResultType FL_CheckSuming_CRC16(uint8 index);
STATIC FL_ResultType FL_CheckSuming_Hash(uint8 index);
STATIC FL_ResultType FL_UpdateNvm(void);
static FL_ResultType FL_CheckCompatibility(void);
/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
uint16 FL_KeepNvmInforNoUsed() {
  uint8 index;
  uint16 sum = 0;

  //    for (index  = 0; index < 64; index++)
  //    {
  //        sum += BL_Information[index];
  //    }

  return sum;
}

/******************************************************************************/
/**
 * @brief               <flashloader module initialize>
 *
 * <initialize download status> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>
 */
/******************************************************************************/
void FL_InitState(void) {
  uint8 i;
  if (0 == FL_KeepNvmInforNoUsed()) {
    // return;
  }
  /*read data from data flash*/
  // FL_ReadMemory(DID_BASEADD,DID_BUFF_LENGTH,DIDDataCopy);
  /* fingerprint is not written */
  FldownloadStatus.fingerPrintWritten = TRUE;

#if (FLS_USED == STD_OFF)
  /* flash driver is not downloaded */
  FldownloadStatus.flDrvDownloaded = FALSE;
#else
  FldownloadStatus.flDrvDownloaded = TRUE;
#endif
  /* current block is not erased */
  FldownloadStatus.blockErased = FALSE;

  /* download step is download request 0x34 */
  FldownloadStatus.downloadStep = FL_REQUEST_STEP;

  /* current active job is idle */
  FldownloadStatus.activeJob = FL_JOB_IDLE;

  FL_RemainDataStruct.remainLength = 0x00ul;

  return;
}

void FL_ReadConstDIDData(uint8 *readData, uint32 Addr, uint8 length) {
  uint8 index;
  uint8 readDataBuf = 0;
  for (index = 0; index < length; index++) {
    // readData[index] = DIDTemp[startIndex + index];
    readDataBuf = *(uint8 *)Addr;
    *readData = readDataBuf;
    Addr++;
    readData++;
  }
}

#if 0
	void FlashReadMemory(uint8* DataBuf,uint32 Addr, uint32 Length)
	{
	    while(Length > 0)
	    {
	        *DataBuf = FlashReadByte(Addr);
	        Addr++;
	        DataBuf++;
	        Length--;
	    }
	}

	uint8 FlashReadByte(uint32 globalAddr)
	{
	    uint8 readData = 0;

		/* read data in local address */
		readData = *(uint8 *)globalAddr;

	    return readData;
	}
#endif

uint32 FL_ReadMemory(uint32 address, uint32 length, uint8 *data) {
  uint32 readLength = 0x00u;
  uint8 curBlockIndex = 0;

  /* read data from flash block */
  for (curBlockIndex = 0; curBlockIndex < FL_NUM_LOGICAL_BLOCKS;
       curBlockIndex++) {
    /* check if address is in range of logical blocks */
    if ((address >= FL_BlkInfo[curBlockIndex].address) &&
        (address < (FL_BlkInfo[curBlockIndex].address +
                    FL_BlkInfo[curBlockIndex].length))) {
      FlashReadMemory(data, address, length);
      readLength = length;
    }

    /* check if address is for did data */
    if ((address >= 0xAF010000) && (address <= 0xAF01FFFF)) {
      FlashReadMemory(data, address, length);
      readLength = length;
    }
  }
  /* read data from header */
  if ((address >= (&SecureSignHeader.datas)) &&
      (address < (&SecureSignHeader.datas + SECURE_HEADER_LEN))) {
    while ((length > 0) &&
           (address < (&SecureSignHeader.datas + SECURE_HEADER_LEN))) {
      /* read data from local address in RAM,direct read and write */
      *data = *(uint8 *)address;
      data++;
      address++;
      length--;
      readLength++;
    }
  }

  /* read data from RAM of flash driver */
  if ((address >= FL_DEV_BASE_ADDRESS) &&
      (address < (FL_DEV_BASE_ADDRESS + FL_DEV_SIZE))) {
    while ((length > 0) && (address < (FL_DEV_BASE_ADDRESS + FL_DEV_SIZE))) {
      /* read data from local address in RAM,direct read and write */
      *data = *(uint8 *)address;
      data++;
      address++;
      length--;
      readLength++;
    }
  }

  /*锟斤拷锟斤拷址锟角凤拷锟斤拷刷写remain锟斤拷锟斤拷锟斤拷*/
  if ((address >= (uint32)&FlProgramData[0]) &&
      (address <= (uint32)&FlProgramData[FL_PROGRAM_SIZE - 1])) {
    while ((length > 0) &&
           (address < (uint32)&FlProgramData[FL_PROGRAM_SIZE - 1])) {
      /* read data from local address in RAM,direct read and write */
      *data = *(uint8 *)address;
      data++;
      address++;
      length--;
      readLength++;
    }
  }
  return readLength;
}

/******************************************************************************/
/**
 * @brief               <0x31 check program pre-condition>
 *
 * <0x31 check program pre-condition .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <conditions (OUT)>
 * @param[in/out]       <NONE>
 * @return              <uint8>
 */
/******************************************************************************/
uint8 FL_CheckProgPreCondition(uint8 *conditions) {
  *conditions = 0x00u;

  return 0x00u;
}

uint8 Fl_crc = 1;
FL_ResultType FL_CheckSumRoutine(const boolean isHash) {

  FL_ResultType ret = FL_OK;

  /* record checksum buffer address */
  // FldownloadStatus.dataBuff = checkSum;
  FlIntegrityChkIsHash = isHash;
  /* check if download step is checksum step */
  if (FL_CHECKSUM_STEP == FldownloadStatus.downloadStep) {
    /* set the download active state to checksum */
    FldownloadStatus.activeJob = FL_JOB_CHECKING;
    FldownloadStatus.errorCode = FL_OK;
    if (FldownloadStatus.flDrvDownloaded == TRUE) {
      if (FL_RemainDataStruct.remainLength != 0x00ul)
        ret = FL_DownloadRemainData();
      else
        ret = FL_OK;
    }
  } else {
    ret = FL_ERR_SEQUENCE;

    /* initialize the flash download state */
    FL_InitState();
  }

  return ret;
}

FL_ResultType FL_CheckCPBRoutine(void) {
  FL_ResultType ret = FL_OK;
  FldownloadStatus.activeJob = FL_JOB_COMPATIBLE;
  Appl_UpdateTriggerConditionImmediate(1);
  return ret;
}
FL_ResultType FBL_IntegrityCheck(void) {
  FL_ResultType ret;
  uint8 idx = 0;
  uint8 someSignVerifiedFlag = FBL_FALSE;
  // if bypass is opened or file is not app
  // for(idx = 0; idx < 5u; idx++)
  {
    if ((1u == FBL_DownloadFlags[idx]) && (1u == FBL_SignVerifFlags[idx])) {
      someSignVerifiedFlag = FBL_TRUE;
      // break;
    }
  }
  if ((FL_CHECKSUM_STEP == FldownloadStatus.downloadStep) ||
      ((FL_REQUEST_STEP == FldownloadStatus.downloadStep) &&
       (FBL_TRUE == someSignVerifiedFlag))) {
    // if ((FBL_TRUE == FBL_BypassFlag))
    // {
    //     ret = FL_CheckSumRoutine(FALSE);
    // }
    // else if (FBL_TRUE == someSignVerifiedFlag)
    if (FBL_TRUE == someSignVerifiedFlag) {
      ret = FL_CheckSumRoutine(TRUE);
    } else {
      Fl_crc = 1;
      ret = FL_OK;
    }
  } else {
    ret = FL_ERR_SEQUENCE;
  }
  // FldownloadStatus.downloadStep = FL_REQUEST_STEP;
  return ret;
}
/******************************************************************************/
/**
 * @brief               <0x31 service routine erase>
 *
 * <erase routine for  logical block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <blockIndex (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
FL_ResultType FL_EraseRoutine(const uint8 blockIndex) {
  FL_ResultType ret = FL_OK;
  FBL_DownloadFlags[0] = 1;
  /* check if finger print is written */
  if (FALSE == FldownloadStatus.fingerPrintWritten) {
    ret = FL_NO_FINGERPRINT;
  } else {
    /* check if flash driver is downloaded */
    if (FALSE == FldownloadStatus.flDrvDownloaded) {
      ret = FL_NO_FLASHDRIVER;
    } else {
      /* check download step sequence */
      if (FL_REQUEST_STEP != FldownloadStatus.downloadStep) {
        // 一锟斤拷锟竭硷拷block锟斤拷锟斤拷锟疥，锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷校锟介，也锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷锟斤拷一锟斤拷锟竭硷拷block锟侥诧拷锟斤拷
        if (FldownloadStatus.downloadStep == FL_CHECKSUM_STEP) {
          if (FL_RemainDataStruct.remainLength != 0x00ul)
            ret = FL_DownloadRemainData();

        } else {
          ret = FL_ERR_SEQUENCE;
        }
      }
    }
  }

  /* check the conditions of erase */
  if (FL_OK == ret) {
    /* check the erase block index and the pragram attempt*/
    if (blockIndex < FL_NUM_LOGICAL_BLOCKS) {
      if (((FL_NvmInfo.blockInfo[blockIndex].blkProgAttempt <
            FL_BlkInfo[blockIndex].maxProgAttempt) ||
           (0x00u == FL_BlkInfo[blockIndex].maxProgAttempt))) {
        /* set current block is valid */
        FL_NvmInfo.blockInfo[blockIndex].blkValid = FALSE;

        /* set the Compatibility flag related to current block  invalid */
        switch (blockIndex) {
        case APPBLOCKINDEX: {
          FL_NvmInfo.isAppCalCpb = TRUE;
          FL_NvmInfo.isAppFblCpb = FALSE;
          appblkIntDefault = TRUE;
          appblkCpbDefault = TRUE;
          break;
        }
          //  case CALDATABLOCKINDEX:
          //  {
          // 	 FL_NvmInfo.isAppCalCpb=FALSE;

          // 	 break;
          //  }
        }
        if (addblkProgAttempt == TRUE) {
          /* increment program attempt counter of current block */
          FL_NvmInfo.blockInfo[blockIndex].blkProgAttempt++;
          addblkProgAttempt = FALSE;
        } else {
          /* do nothing */
        }

        /* store fingerprint of current block */
        Appl_Memcpy(FL_NvmInfo.blockInfo[blockIndex].fingerPrint,
                    FldownloadStatus.fingerPrint, FL_FINGER_PRINT_LENGTH);

        /* change and initialize status of the programmed block */

        FldownloadStatus.segmentList[blockIndex].nrOfSegments = 0x00u;
        FldownloadStatus.segmentList[blockIndex].blockChecked = FALSE;

        FldownloadStatus.blockIndex = blockIndex;
        FldownloadStatus.blockErased = FALSE;
        FldownloadStatus.errorCode = FL_OK;

        /* set the download active state to erase */
        FldownloadStatus.activeJob = FL_JOB_ERASING;
      } else {
        ret = FL_FAILED;
      }
    } else {
      ret = FL_INVALID_DATA;
    }
  }

  if (ret != FL_OK) {
    /* initialize the flash download state */
    FL_InitState();
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <0x34 service download request>
 *
 * <download request for current logical block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <startAdd (IN), length (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
uint8 FLERRStatus = 0;
FL_ResultType FL_DownloadRequestValid(const uint32 startAdd,
                                      const uint32 length) {
  FL_ResultType ret = FL_OK;

  /* save the parameter used for active job use */
  FldownloadStatus.startAddr = startAdd;
  FldownloadStatus.length = length;

  /* check if finger print is written */
  if (FALSE == FldownloadStatus.fingerPrintWritten) {
    ret = FL_NO_FINGERPRINT;
  } else {
    /* check download step sequence */
    if ((FL_REQUEST_STEP != FldownloadStatus.downloadStep) &&
        (FL_CHECKSUM_STEP != FldownloadStatus.downloadStep)) {
      ret = FL_ERR_SEQUENCE;
    } else {
      if (0 == FldownloadStatus.length) {
        ret = FL_ERR_ADDR_LENGTH;
      }
    }
  }

  /* check the condition for download request */
  if (FL_OK == ret) {
    /* check if flash driver is downloaded */
    if (FALSE == FldownloadStatus.flDrvDownloaded) {
      /* check if download address is in flash driver RAM range */
      if ((FL_DEV_BASE_ADDRESS == FldownloadStatus.startAddr) &&
          (FldownloadStatus.length <= FL_DEV_SIZE)) {
        /* set the download step*/
        FldownloadStatus.downloadStep = FL_TRANSFER_STEP;

        FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments =
            0x01u;
        FldownloadStatus.segmentList[CurrentProgrammingBlock]
            .segmentInfo[0]
            .address = FldownloadStatus.startAddr;
        FldownloadStatus.segmentList[CurrentProgrammingBlock]
            .segmentInfo[0]
            .length = FldownloadStatus.length;
      } else {
        ret = FL_NO_FLASHDRIVER;
      }
    } else {
      /* check if download address is in correct range */
      ret = FL_CheckDownloadSegment();

      if (FL_OK == ret) {
        ret = FL_HandleRemainData();
        /* Signature Header */
        if (FldownloadStatus.blockIndex == 0) {
          /* set the download step*/
          FldownloadStatus.downloadStep = FL_HEADER_TRASNFER_STEP;
        }
        /* APP File */
        else if (FldownloadStatus.blockIndex == 1) {
          /* set the download step*/
          FldownloadStatus.downloadStep = FL_TRANSFER_STEP;
        }
      }
    }
  }

  if (ret != FL_OK) {
    /* initialize the flash download state */
    FL_InitState();
  }

  FldownloadStatus.activeJob = FL_JOB_IDLE;
  FLERRStatus = ret;
  return ret;
}

/******************************************************************************/
/**
 * @brief               <0x36 service download data>
 *
 * <download data for current logical block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <destAddr (IN), sourceBuff (IN), length (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
FL_ResultType FL_FlashProgramRegion(const uint32 destAddr,
                                    const uint8 *sourceBuff,
                                    const uint32 length) {
  FL_ResultType ret = FL_OK;
  FL_ResultType result;
  /* change local address to global address*/
  uint32 globalAddr = destAddr;

  /* check the conditions of the program*/
  if ((FL_TRANSFER_STEP != FldownloadStatus.downloadStep) &&
      (FL_HEADER_TRASNFER_STEP != FldownloadStatus.downloadStep)) {
    ret = FL_ERR_SEQUENCE;
  } else {
    if ((FldownloadStatus.startAddr != globalAddr) ||
        (FldownloadStatus.length < length)) {
      ret = FL_ERR_ADDR_LENGTH;
    }
  }

  if (FL_OK == ret) {
    /* check if flash driver is downloaded */
    if (FALSE == FldownloadStatus.flDrvDownloaded) {
      /* copy the data to the request address*/
      Appl_Memcpy((uint8 *)globalAddr, sourceBuff, (uint32)length);

      /* index start address and length */
      FldownloadStatus.startAddr += length;
      FldownloadStatus.length -= length;

      /* check if flash driver download is finished */
      if (0x00uL == FldownloadStatus.length) {
        /* set the download step*/
        FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
      }

      FldownloadStatus.activeJob = FL_JOB_IDLE;
    }
    /* Header */
    else if ((globalAddr >= FL_BlkInfo[0].address) &&
             (globalAddr < (FL_BlkInfo[0].address + FL_BlkInfo[0].length))) {
      FBL_HeaderType = HEADER_TYPE_APP;
      result = Secure_SaveSignHeader(sourceBuff, length, FBL_HeaderType);
      FldownloadStatus.startAddr += length;
      FldownloadStatus.length -= length;
      if ((SECURE_SAVE_FINISH == result) &&
          (0x00uL == FldownloadStatus.length)) {
        /* set the download step*/
        FldownloadStatus.downloadStep = FL_HEADER_EXIT_TRANSFER_STEP;
      } else if (SECURE_SAVE_OVERFLOW == result) {
        ret = FL_ERR_ADDR_LENGTH;
      }
      // else
      // {
      //     ret = FL_DOWNLOAD_HEADER;
      // }
      FldownloadStatus.activeJob = FL_JOB_IDLE;

      // Appl_Memcpy( &FL_Header_Buffer[globalAddr], sourceBuff, (uint32)length
      // ); FldownloadStatus.startAddr += length;
      // FldownloadStatus.length -= length;
      // if (0x00uL == FldownloadStatus.length)
      // {
      //     /* set the download step*/
      //     FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
      // }
      // FldownloadStatus.activeJob = FL_JOB_IDLE;
    }
    /* CALIBRATION Header*/
    else if ((globalAddr >= FL_BlkInfo[2].address) &&
             (globalAddr < (FL_BlkInfo[2].address + FL_BlkInfo[2].length))) {
                FBL_HeaderType = HEADER_TYPE_CAL;
                result = Secure_SaveSignHeader(sourceBuff, length, FBL_HeaderType);

    } else {
      /* save parameter for program active job */
      FldownloadStatus.dataBuff = sourceBuff;
      FlProgramLength = length;

      /* set the download active state to program*/
      FldownloadStatus.activeJob = FL_JOB_PROGRAMMING;
      FldownloadStatus.errorCode = FL_OK;
    }
  }

  if (ret != FL_OK) {
    /* initialize the flash download state */
    FL_InitState();
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <0x36 service download data>
 *
 * <download data for current logical block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <destAddr (IN), sourceBuff (IN), length (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
void FL_SetExitTransferStep(void) {
  FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
  FldownloadStatus.activeJob = FL_JOB_IDLE;
}

/******************************************************************************/
/**
 * @brief               <0x36 service download data>
 *
 * <download data for current logical block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <destAddr (IN), sourceBuff (IN), length (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
void FL_SetHeadBlockErased(void) {
  FldownloadStatus.blockErased = TRUE;
  FldownloadStatus.activeJob = FL_JOB_IDLE;
  FldownloadStatus.errorCode = FL_OK;
}

/******************************************************************************/
/**
 * @brief               <0x37 service download finish>
 *
 * <download finish for current logical block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
FL_ResultType FL_ExitTransferData(void) {
  FL_ResultType ret = FL_OK;
  FL_ResultType signVerifRet;
  if (FL_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep) {
    /* set the download step*/
    FldownloadStatus.downloadStep = FL_CHECKSUM_STEP;
    FldownloadStatus.activeJob = FL_JOB_IDLE;
  } else if (FL_HEADER_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep) {
    /* Sign Verification */
    signVerifRet = Secure_SignHeaderChk(&fblFileIndex);
    //        signVerifRet = SECURE_PASS;
    if (SECURE_ERRORSEQUENCE == signVerifRet) {
      ret = FL_ERR_SEQUENCE;
    } else {
      ret = signVerifRet + FBL_SECURE_NRC_OFFSET;
    }

    if (SECURE_PASS == signVerifRet) {
      FBL_SignVerifFlags[0] = FBL_TRUE;
      ret = FL_OK;
    } else {
      Secure_Init();
    }
    /* set the request step*/
    // FldownloadStatus.downloadStep = FL_REQUEST_STEP;
    // FldownloadStatus.activeJob = FL_JOB_IDLE;
  } else {
    /* initialize the flash download state */
    ret = FL_ERR_SEQUENCE;
    FL_InitState();
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <get service status>
 *
 * <supply active job status for service witch can not response with in 50ms,
 *  and send pending> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <NON Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <error (OUT)>
 * @param[in/out]       <NONE>
 * @return              <boolean>
 */
/******************************************************************************/
boolean FL_ServiceFinished(FL_ResultType *error) {
  boolean ret = FALSE;

  /* check if service is finished */
  if (FL_JOB_IDLE == FldownloadStatus.activeJob) {
    *error = FldownloadStatus.errorCode;
    ret = TRUE;
  } else {
    *error = FL_OK;
    ret = FALSE;
  }

  return ret;
}

/******************************************************************************/
/**
 * 锟斤拷锟斤拷锟斤拷锟藉：使锟斤拷锟斤拷锟斤拷37锟斤拷锟斤拷锟斤拷busy状态
 *
 */
/******************************************************************************/
void SetS37JobStatusBusy(void) {
  FldownloadStatus.activeJob = FL_JOB_S37CHECKING;
}

/******************************************************************************/
/**
 * 锟斤拷锟斤拷锟斤拷锟藉：锟斤拷取37锟斤拷锟斤拷前状态
 *
 */
/******************************************************************************/
void GetS37JobStatusResult(FL_ResultType status) {
  FldownloadStatus.activeJob = FL_JOB_IDLE;
  FldownloadStatus.errorCode = status;
}

/******************************************************************************/
/**
 * 锟斤拷锟斤拷锟斤拷锟藉：锟斤拷锟斤拷37锟斤拷锟斤拷锟紺RC32校锟斤拷
 *
 */
/******************************************************************************/
boolean headRemainDataDownloaded = FALSE;
FL_ResultType FL_CheckSumFor37(SecM_CRCType *crc) {
  FL_ResultType ret = FL_OK;
  SecM_VerifyParamType verifyParam;
  SecM_StatusType crcret = SECM_OK;
  uint16 l_nrOfSegments;
  uint32 currentaddr, currentlength;
  FL_SegmentInfoType remainsegment;

  /*add by fhq 20210326*/
  FL_SegmentListType segmentList;
  verifyParam.segmentList = &segmentList;

  if (EXTERNAL_FLS == FL_BlkInfo[FldownloadStatus.blockIndex].flashtype) {

    //		if(FL_RemainDataStruct.remainLength>0)
    //		{
    //			remainsegment.address=(uint32)(&FlProgramData[0]);
    //			remainsegment.length=FL_RemainDataStruct.remainLength;
    //			crcret=extFls_CheckSumFor37(crc,&remainsegment);
    //		}
    //		else
    //		{
    //			crcret=extFls_CheckSumFor37(crc,NULL);
    //		}
    //
    //		if(crcret==SECM_OK)
    //		{
    //			return FL_OK;
    //		}
    //		else
    //		{
    //			return FL_FAILED;
    //		}
    if (FALSE == headRemainDataDownloaded) {
      headRemainDataDownloaded = TRUE;
    }
  }

  l_nrOfSegments =
      FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments;
  currentaddr = FldownloadStatus.segmentList[CurrentProgrammingBlock]
                    .segmentInfo[l_nrOfSegments - 1]
                    .address;
  currentlength = FldownloadStatus.segmentList[CurrentProgrammingBlock]
                      .segmentInfo[l_nrOfSegments - 1]
                      .length;

  if (FL_RemainDataStruct.remainLength > 0) // 锟斤拷锟斤拷锟斤拷未写锟斤拷flash
  {
    if (currentaddr <
        FL_RemainDataStruct
            .remainAddr) // flash锟斤拷ram锟叫讹拷锟斤拷锟斤拷锟斤拷
    {
      verifyParam.segmentList->nrOfSegments = 2;
      verifyParam.segmentList->segmentInfo[0].address = currentaddr;
      verifyParam.segmentList->segmentInfo[0].length =
          FL_RemainDataStruct.remainAddr - currentaddr;

      verifyParam.segmentList->segmentInfo[1].address =
          (uint32)(&FlProgramData[0]);
      verifyParam.segmentList->segmentInfo[1].length =
          FL_RemainDataStruct.remainLength;
    } else // 只锟斤拷ram锟斤拷锟斤拷锟斤拷锟斤拷
    {
      verifyParam.segmentList->nrOfSegments = 1;
      verifyParam.segmentList->segmentInfo[0].address =
          (uint32)(&FlProgramData[FL_RemainDataStruct.remainLength -
                                  currentlength]);
      verifyParam.segmentList->segmentInfo[0].length = currentlength;
    }
  } else // 锟斤拷锟斤拷锟斤拷锟捷讹拷锟窖撅拷写锟斤拷锟斤拷flash
  {
    verifyParam.segmentList->nrOfSegments = 1;
    verifyParam.segmentList->segmentInfo[0].address = currentaddr;
    verifyParam.segmentList->segmentInfo[0].length = currentlength;
  }

  crcret = SecM_CRC_Calculate(&verifyParam);
  if (crcret == SECM_OK) {
    *crc = verifyParam.crcTotle;

  } else {
    ret = FL_FAILED;
  }

  return ret;
}
FL_ResultType FBL_CheckSumFor37(uint16 *crcValPtr) {
  FL_ResultType ret;
  /* Try: Assume that the first 34 sends the address of corresponding block */
  if ((FBL_TRUE == FBL_SignVerifFlags[0]) &&
      (FL_HEADER_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep)) {
    ret = Secure_CheckSumForHeader(crcValPtr);
    FldownloadStatus.downloadStep = FL_REQUEST_STEP;
    Secure_Init();
    if (FALSE == headRemainDataDownloaded) {
      headRemainDataDownloaded = TRUE;
    }
  } else if (FL_CHECKSUM_STEP == FldownloadStatus.downloadStep) {
    ret = FL_CheckSumFor37(crcValPtr);
  } else {
    ret = FL_ERR_SEQUENCE;
    FldownloadStatus.downloadStep = FL_REQUEST_STEP;
  }
  return ret;
}
/******************************************************************************/
/**
 * @brief               <get uint32 from data buffer>
 *
 * <get uint32 from data buffer> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <data (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <uint32>
 */
/******************************************************************************/
STATIC uint32 FL_Get4Byte(const uint8 *data) {
  uint32 retData;

  retData = ((uint32)data[0]) << 24;
  retData += ((uint32)data[1]) << 16;
  retData += ((uint32)data[2]) << 8;
  retData += (uint32)data[3];

  return retData;
}

FL_ResultType FL_CheckModuleId(uint8 moduleId, uint8 *locationInfo,
                               uint8 *idxRet) {
  FL_ResultType result = FL_OUT_OF_RANGE;
  uint32 locAdr;
  uint32 locLength;
  uint8 index = 1;

  locAdr = FL_Get4Byte(&locationInfo[2]);
  locAdr = locAdr | 0xa0000000;
  locLength = FL_Get4Byte(&locationInfo[6]);

  // for(index = APP_FL_INDEX; index < FL_NUM_LOGICAL_BLOCKS; index++)
  {
    // if (moduleId == FL_BlkInfo[index].moduleId)
    {
      if ((locAdr == FL_BlkInfo[index].address) &&
          (locLength <= FL_BlkInfo[index].length)) {
        *idxRet = index;
        result = FL_OK;
      } else {
        result = FL_MODULEID_UNMATCH;
      }
      // break;
    }
  }
  return result;
}
/******************************************************************************/
/**
 * @brief               <flash main function for active job>
 *
 * <flash main function for active job,process checksum,erase and program> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>
 */
/******************************************************************************/
void FL_MainFunction(void) {
  switch (FldownloadStatus.activeJob) {
  case FL_JOB_ERASING:
    /* do the flash erase*/
    FldownloadStatus.errorCode = FL_Erasing();
    FldownloadStatus.activeJob = FL_JOB_IDLE;

    break;

  case FL_JOB_PROGRAMMING:
    /* do the flash program*/
    FldownloadStatus.errorCode = FL_Programming();
    FldownloadStatus.activeJob = FL_JOB_IDLE;
    break;

  case FL_JOB_CHECKING:
    /* do the flash checksum*/
    FldownloadStatus.errorCode = FL_CheckSuming();
    FldownloadStatus.activeJob = FL_JOB_IDLE;
    break;
  case FL_JOB_COMPATIBLE: {
    FldownloadStatus.errorCode = FL_CheckCompatibility();
    FldownloadStatus.activeJob = FL_JOB_IDLE;
    break;
  }
  default:
    break;
  }

  if (FldownloadStatus.errorCode != FL_OK) {
    /* initialize the flash download state */
    FL_InitState();
    FldownloadStatus.activeJob = FL_JOB_IDLE;
  }

  return;
}

/******************************************************************************/
/**
 * @brief               <program bootloader infomation to EEPROM>
 *
 * <program bootloader infomation to EEPROM,e.g. block valid,checksum,
 *  fingerprint..> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
STATIC FL_ResultType FL_UpdateNvm(void) {
  FL_ResultType ret = FL_FAILED;
  /* CRC32 parameter */
  SecM_CRCParamType crcParam;

  /* Initialize CRC32 parameter */
  crcParam.crcState = SECM_CRC_INIT;
  crcParam.crcSourceBuffer = (const uint8 *)&FL_NvmInfo;
  crcParam.crcByteCount = sizeof(FL_NvmInfoType) - 6;

  /* compute CRC of the block infomation */
  (void)SecM_ComputeCRC(&crcParam);
  crcParam.crcState = SECM_CRC_COMPUTE;
  (void)SecM_ComputeCRC(&crcParam);
  crcParam.crcState = SECM_CRC_FINALIZE;
  (void)SecM_ComputeCRC(&crcParam);

  /* program computed CRC value to flash */
  FL_NvmInfo.infoChecksum = crcParam.currentCRC;

  /* set input parameter of flash driver interface */
  flashParamInfo.data = (const uint8 *)&FL_NvmInfo;
  flashParamInfo.address = FL_NVM_INFO_ADDRESS;
  flashParamInfo.length = sizeof(FL_NvmInfoType);

  /* align program size */
  if ((flashParamInfo.length & (FLASH_ONE_SECTOR - 1)) > 0) {
    flashParamInfo.length &= ~(FLASH_ONE_SECTOR - 1);
    flashParamInfo.length += FLASH_ONE_SECTOR;
  }

  /* erase flash block witch store the blocks infomation */
  BLFlash_InfoPtr->flashEraseFct(&flashParamInfo);

  if (kFlashOk == flashParamInfo.errorCode) {
    /* program blocks infomation */
    flashParamInfo.length = sizeof(FL_NvmInfoType);
    if ((flashParamInfo.length & (FLASH_ONE_PHRASE - 1)) > 0) {
      flashParamInfo.length &= ~(FLASH_ONE_PHRASE - 1);
      flashParamInfo.length += FLASH_ONE_PHRASE;
    }
    BLFlash_InfoPtr->flashWriteFct(&flashParamInfo);
  }

  if (kFlashOk == flashParamInfo.errorCode) {
    ret = FL_OK;
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <active job erase>
 *
 * <erase the current logical block witch requested by 0x31 service> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/

STATIC FL_ResultType FL_Erasing(void) {
  FL_ResultType ret = FL_OK;

  /* update the bootloader infomation to EEPROM */
  Appl_UpdateTriggerConditionImmediate(1);
  //    ret = FL_UpdateNvm();

  if (FL_OK == ret) {
    { /* set flash driver input parameter */
      flashParamInfo.address = 0xA0050000uL; // CurrentErasingAddress;
      flashParamInfo.length = 0x1b0000uL;    // CurrentErasingLength;
      BLFlash_InfoPtr->flashEraseFct(&flashParamInfo);
    }

    /* check if erase success */
    if (kFlashOk == flashParamInfo.errorCode) {
      /*set the block erased */
      FldownloadStatus.blockErased = TRUE;
    } else {
      ret = FL_FAILED;
    }
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <check segment address>
 *
 * <check if the transfered address is in current block,and if the address is
 *  increased by segment> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
STATIC FL_ResultType FL_CheckDownloadSegment(void) {
  FL_ResultType ret = FL_OK;
  FL_SegmentInfoType *curSegment;
  uint16 segmentIndex; // uint8锟斤拷为uint16

  /*
   ** check if block is erased,if current num of segment is less than maxmun,
   ** if address if in current block.
   */
  if (FALSE == FldownloadStatus.blockErased) {
    ret = FL_ERR_SEQUENCE;
  } else {
    uint16 useSegment = 0;
    useSegment =
        FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments;

    if ((useSegment < FL_MAX_SEGMENTS) &&
        (FldownloadStatus.startAddr ==
         FL_BlkInfo[FldownloadStatus.blockIndex].address) &&
        ((FldownloadStatus.startAddr + FldownloadStatus.length) <=
         (FL_BlkInfo[FldownloadStatus.blockIndex].address +
          FL_BlkInfo[FldownloadStatus.blockIndex].length))) {
      /* get current segment num */
      segmentIndex = useSegment;

      /* check if segment address is increase */
      if (segmentIndex > 0x00u) {
        curSegment = &FldownloadStatus.segmentList[CurrentProgrammingBlock]
                          .segmentInfo[segmentIndex - 1];

        /* check if download address is in front segment range */
        if (FldownloadStatus.startAddr <
            (curSegment->address + curSegment->length)) {
          ret = FL_ERR_ADDR_LENGTH;
        }
      }

      if (FL_OK == ret) {
        /* set the flash download info */
        curSegment = &FldownloadStatus.segmentList[CurrentProgrammingBlock]
                          .segmentInfo[segmentIndex];
        FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments++;

        curSegment->address = FldownloadStatus.startAddr;
        curSegment->length = FldownloadStatus.length;
      }
    } else {
      ret = FL_ERR_ADDR_LENGTH;
    }
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <FL_DownloadRemainData>
 *
 *
 *
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
FL_ResultType FL_DownloadRemainData(void) {
  FL_ResultType ret = FL_OK;

  /*two segment is not in one page or checking sum is started,so download the
   * last segment first*/
  flashParamInfo.address = FL_RemainDataStruct.remainAddr;
  /* fill pad for the left data */
  Appl_Memset(&FlProgramData[FL_RemainDataStruct.remainLength],
              (const uint8)FL_GAP_FILL_VALUE,
              FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength);

  /* set the flash download info */
  flashParamInfo.data = &FlProgramData[0];
  flashParamInfo.length = FL_PROGRAM_SIZE;
  FL_RemainDataStruct.remainLength = 0x00ul;
  /* write the last 0x36 aligned data */
  {
    BLFlash_InfoPtr->flashWriteFct(&flashParamInfo);
  }

  if (flashParamInfo.errorCode != kFlashOk) {
    ret = FL_FAILED;
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <FL_HandleRemainData>
 *
 *
 *
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
FL_ResultType FL_HandleRemainData(void) {
  FL_ResultType ret = FL_OK;

  if (FL_RemainDataStruct.remainLength != 0x00ul) {
    if ((FL_RemainDataStruct.remainAddr & ~(FL_PROGRAM_SIZE - 1ul)) ==
        (FldownloadStatus.startAddr & ~(FL_PROGRAM_SIZE - 1ul))) {
      /*link the remain data and new segment because one page, and download
       * together via 0x36*/
      Appl_Memset(&FlProgramData[FL_RemainDataStruct.remainLength],
                  (const uint8)FL_GAP_FILL_VALUE,
                  FldownloadStatus.startAddr -
                      FL_RemainDataStruct.remainLength -
                      FL_RemainDataStruct.remainAddr);
      FL_RemainDataStruct.remainLength =
          FldownloadStatus.startAddr - FL_RemainDataStruct.remainAddr;
      ;
    } else {
      ret = FL_DownloadRemainData();
      FL_RemainDataStruct.remainLength =
          FldownloadStatus.startAddr & (FL_PROGRAM_SIZE - 1);
      /* initialize the program buffer */
      Appl_Memset(&FlProgramData[0], (uint8)FL_GAP_FILL_VALUE,
                  (uint32)FL_PROGRAM_SIZE);
    }
  } else {
    FL_RemainDataStruct.remainLength =
        FldownloadStatus.startAddr & (FL_PROGRAM_SIZE - 1);
    /* initialize the program buffer */
    Appl_Memset(&FlProgramData[0], (uint8)FL_GAP_FILL_VALUE,
                (uint32)FL_PROGRAM_SIZE);
  }
  return ret;
}

/******************************************************************************/
/**
 * @brief               <program data>
 *
 * <program the aligned data transfered by 0x36 service request > .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
STATIC FL_ResultType FL_ProgrammingData(void) {
  FL_ResultType ret = FL_OK;

  /* check the program length and program status */
  while ((FlProgramLength > 0) && (FL_OK == ret)) {
    /* check if the program size is more than maxmun size of program buffer */
    if ((FlProgramLength + FL_RemainDataStruct.remainLength) >=
        FL_PROGRAM_SIZE) {
      /* get the download datas */
      Appl_Memcpy(&FlProgramData[FL_RemainDataStruct.remainLength],
                  FldownloadStatus.dataBuff,
                  FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength);

      /* index the databuff point in tranfered buffer */
      FldownloadStatus.dataBuff +=
          FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;

      /* index the totle program length */
      FlProgramLength -= FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;

      /* set the flash driver input parameter */
      flashParamInfo.address =
          FldownloadStatus.startAddr - FL_RemainDataStruct.remainLength;
      flashParamInfo.length = (uint32)FL_PROGRAM_SIZE;
      flashParamInfo.data = (uint8 *)&FlProgramData[0];

      /* program the data */
      if (TRUE == headRemainDataDownloaded) {
        BLFlash_InfoPtr->flashWriteFct(&flashParamInfo);
      }

      /* index the start address and length that record in 0x34 service */
      FldownloadStatus.startAddr +=
          FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;
      FldownloadStatus.length -=
          FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;

      FL_RemainDataStruct.remainLength = 0x00ul;
      /* check if program success */
      if (flashParamInfo.errorCode != kFlashOk) {
        ret = FL_FAILED;
      }
    } else {
      /* set the last datas for write of current service 0x36 */
      Appl_Memcpy(&FlProgramData[FL_RemainDataStruct.remainLength],
                  FldownloadStatus.dataBuff, (uint32)FlProgramLength);
      FL_RemainDataStruct.remainAddr =
          FldownloadStatus.startAddr - FL_RemainDataStruct.remainLength;
      FL_RemainDataStruct.remainLength += FlProgramLength;
      /* index the start address and length that record in 0x34 service */
      FldownloadStatus.startAddr += FlProgramLength;
      FldownloadStatus.length -= FlProgramLength;
      /* end of current service 0x36 */
      FlProgramLength = 0;
    }
  }

  return ret;
}

/******************************************************************************/
/**
 * @brief               <active job program>
 *
 * <program the data transfered by 0x36 service request > .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
STATIC FL_ResultType FL_Programming(void) {
  FL_ResultType ret = FL_OK;

  /* program buffer aligned data */
  ret = FL_ProgrammingData();

  /* check if the last not aligned data should be programmed */
  if ((FldownloadStatus.length == 0x00uL) && (FL_OK == ret)) {
    /* program the not aligned data */
    // ret = FL_ProgrammingAlignData();
    FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
  }
  return ret;
}

/******************************************************************************/
/**
 * @brief               <fill pad>
 *
 * <fill the pad between segments of current block> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
#if (FL_USE_GAP_FILL == STD_ON)
STATIC FL_ResultType FL_FillGap(void) {
  FL_ResultType ret = FL_OK;
  uint8 segmentIndex = 0;
  uint32 startAddress = FL_BlkInfo[FldownloadStatus.blockIndex].address;
  uint32 gapLength;

  /* set the download datas with FL_GAP_FILL_VALUE */
  Appl_Memset((uint8 *)&FlProgramData[0], (uint8)FL_GAP_FILL_VALUE,
              (uint32)FL_PROGRAM_SIZE);

  flashParamInfo.data = (uint8 *)&FlProgramData[0];

  while ((segmentIndex <= FldownloadStatus.segmentList.nrOfSegments) &&
         (FL_OK == ret)) {
    /* find length ofthe gap in the segment*/
    if (segmentIndex < FldownloadStatus.segmentList.nrOfSegments) {
      gapLength =
          FldownloadStatus.segmentList.segmentInfo[segmentIndex].address -
          startAddress;
    } else {
      gapLength = (FL_BlkInfo[FldownloadStatus.blockIndex].address +
                   FL_BlkInfo[FldownloadStatus.blockIndex].length) -
                  startAddress;
    }

    gapLength &= ~(FL_FLASH_ALIGN_SIZE - 1);

    /* set the flash download address of gap */
    flashParamInfo.address = startAddress;

    while ((gapLength > 0) && (FL_OK == ret)) {
      if (gapLength >= FL_PROGRAM_SIZE) {
        /* set the download length */
        flashParamInfo.length = FL_PROGRAM_SIZE;

        /* update the gap length*/
        gapLength -= FL_PROGRAM_SIZE;
      } else {
        /* the last gap*/
        flashParamInfo.length = gapLength;
        gapLength = 0;
      }

      /* write the flash of the FlashParam for gap*/
      BLFlash_Info.flashWriteFct(&flashParamInfo);

      flashParamInfo.address += flashParamInfo.length;

      /* check if program pad success */
      if (flashParamInfo.errorCode != kFlashOk) {
        ret = FL_FAILED;
      }
    }

    if (segmentIndex < FldownloadStatus.segmentList.nrOfSegments) {
      /* set the next start address */
      startAddress =
          FldownloadStatus.segmentList.segmentInfo[segmentIndex].address +
          FldownloadStatus.segmentList.segmentInfo[segmentIndex].length;

      if ((startAddress & (FL_FLASH_ALIGN_SIZE - 1)) > 0) {
        startAddress &= ~(FL_FLASH_ALIGN_SIZE - 1);
        startAddress += FL_FLASH_ALIGN_SIZE;
      }
    }

    segmentIndex++;
  }

  return ret;
}
#endif

/******************************************************************************/
/**
 * @brief               <active job checksum>
 * 之前刷锟斤拷锟剿讹拷锟斤拷锟斤拷锟捷撅拷校锟斤拷锟斤拷锟斤拷锟斤拷锟�
 *
 * <active checksum that request by 0x31 service> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
uint8 FBL_GetSignVerifFlag(uint8 index) { return FBL_SignVerifFlags[index]; }
STATIC FL_ResultType FL_CheckSuming(void) {
  FL_ResultType ret = FL_OK;
  FL_ResultType checkStatus;
  SecM_VerifyParamType verifyParam;
  uint16 l_nrOfSegments;
  int i = 0;
  uint8 failcount = 0;

  for (i = 1; i < FL_NUM_LOGICAL_BLOCKS; i++) {
    if (FldownloadStatus.segmentList[i].nrOfSegments != 0) {
      //  l_nrOfSegments= FldownloadStatus.segmentList[i].nrOfSegments;
      //  verifyParam.segmentList = &FldownloadStatus.segmentList[i];

      if (TRUE == FlIntegrityChkIsHash) {
        if (1u == FBL_GetSignVerifFlag(0)) {
          checkStatus = FL_CheckSuming_Hash(1);
        } else {
          checkStatus = FL_FAILED;
        }
      }
      //  else
      //  {
      //      checkStatus = FL_CheckSuming_CRC16(i);
      //  }
      // checkStatus = SECM_OK;//ignore sign
      FldownloadStatus.segmentList[i].blockChecked = TRUE;
      appblkIntDefault = FALSE;
      //             checkStatus=SECM_OK;
      if (SECM_OK == checkStatus) {
        Fl_crc = 0;
        /* check if flash driver is downloaded */
        if (FALSE == FldownloadStatus.flDrvDownloaded) {
          /* flash driver initialize */
          BLFlash_InfoPtr->flashInitFct(&flashParamInfo);
          /* check if flash driver is initialized success */
          if (flashParamInfo.errorCode != kFlashOk) {
            failcount++;
            break;
          } else {
            FldownloadStatus.flDrvDownloaded = TRUE;
          }
        } else {
          /* set current block is valid */
          FL_NvmInfo.blockInfo[i].blkValid = TRUE;

          /* save computed CRC to NVM if CRC success */
          FL_NvmInfo.blockInfo[i].blkChecksum = verifyParam.crcTotle;
        }
      } else {
        /* set current block is valid */
        FL_NvmInfo.blockInfo[i].blkValid = FALSE;

        /* save computed CRC to NVM if CRC success */
        FL_NvmInfo.blockInfo[i].blkChecksum = verifyParam.crcTotle;

        failcount++;
      }
      /* update the flashinfo in eep*/
      // Appl_UpdateTriggerConditionImmediate(1);

      /* check if EEPROM UPDATE failed */
      if (ret != FL_OK) {
        failcount++;
        FL_NvmInfo.blockInfo[i].blkValid = FALSE;
      }
    } else {
      FldownloadStatus.segmentList[i].blockChecked = FALSE;
    }
  }
  ret = FL_UpdateNvm();

  if (ret != FL_OK) {
    failcount++;
  }

  if (failcount > 0) {

    ret = FL_FAILED;
  } else {
    ret = FL_OK;
  }
  FldownloadStatus.downloadStep = FL_REQUEST_STEP;
  return ret;
}

/******************************************************************************/
/**
 * @brief               <active job CRC16 check>
 *
 * <active checksum that request by 0x31 service> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
STATIC FL_ResultType FL_CheckSuming_CRC16(uint8 index) {
  SecM_VerifyParamType verifyParam;
  uint16 l_nrOfSegments;
  FL_ResultType checkStatus;
  uint8 RequestResultPtr = 0;

  //     l_nrOfSegments= FldownloadStatus.segmentList[index].nrOfSegments;
  //     verifyParam.segmentList = &FldownloadStatus.segmentList[index];

  //     switch (FL_BlkInfo[index].crcaddrtype)
  //     {
  //     case NO_CRC:
  //     {
  //        break;
  //     }
  //     case LAST_ADDR:
  //     {
  //        verifyParam.verificationData = (uint8
  //        *)FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments -
  //        1].address; verifyParam.verificationData +=
  //        FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments -
  //        1].length;
  // #if (CAL_CRC32 == CAL_METHOD)
  //        verifyParam.verificationData -= 4; /* last 4 byte is CRC value */
  //        FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments -
  //        1].length -= 4;
  // #else
  //        verifyParam.verificationData -= 2; /* last 2 byte is CRC value */
  //        FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments -
  //        1].length -= 2;
  // #endif
  //        break;
  //     }
  //     case HEAD_ADDR:
  //     {
  // #if (CAL_CRC32 == CAL_METHOD)
  //        verifyParam.verificationData = (uint8
  //        *)FldownloadStatus.segmentList[index].segmentInfo[0].address;
  //        FldownloadStatus.segmentList[index].segmentInfo[0].address += 4;
  //        FldownloadStatus.segmentList[index].segmentInfo[0].length -= 4;

  // #else
  //        FldownloadStatus.segmentList[index].segmentInfo[0].address += 2;
  //        FldownloadStatus.segmentList[index].segmentInfo[0].length -= 2;
  //        verifyParam.verificationData = (uint8
  //        *)FldownloadStatus.segmentList[index].segmentInfo[0].address;
  // #endif
  //        break;
  //     }
  //     }

  //     /* Execute the CRC16 */
  //     checkStatus = SecM_Verification(&verifyParam);

  //     if (index == 0x00u)
  //     {
  //        Ram_B301[1] = 1;
  //        Ram_B301[2] = 0;
  //        Ram_B301[3] = 0;
  //        Ram_B301[4] = 0;
  //        Ram_B301[5] = 0;
  //     }
  //     else if (index == 0x01u)
  //     {
  //        Ram_B301[26] = 0x5A;
  //        Ram_B301[27] = 0;
  //        Ram_B301[28] = 0;
  //        Ram_B301[29] = 0;
  //        Ram_B301[30] = 0;
  //     }
  //     else
  //     {
  //        Ram_B301[(1 + 5 * (index - 1))] = index;
  //        Ram_B301[(2 + 5 * (index - 1))] = 0;
  //        Ram_B301[(3 + 5 * (index - 1))] = 0;
  //        Ram_B301[(4 + 5 * (index - 1))] = 0;
  //        Ram_B301[(5 + 5 * (index - 1))] = 0;
  //     }

  //     if (SECM_OK == checkStatus)
  //     {
  //        if (index == 0x00u)
  //        {
  //            Ram_B301[4] = verifyParam.verificationData[0];
  //            Ram_B301[5] = verifyParam.verificationData[1];
  //        }
  //        else if (index == 0x01u)
  //        {
  //            Ram_B301[29] = verifyParam.verificationData[0];
  //            Ram_B301[30] = verifyParam.verificationData[1];
  //        }
  //        else
  //        {
  //            Ram_B301[(4 + 5 * (index - 1))] =
  //            verifyParam.verificationData[0]; Ram_B301[(5 + 5 * (index - 1))]
  //            = verifyParam.verificationData[1];
  //        }
  //     }
  //     // NvM_WriteBlock(EEP_DIDB301_BLOCK_ID,NULL);
  // 	// NvM_WriteAll();
  // 	// do
  // 	// {
  // 	// 	#include "Appl.h"

  // 	// 	Appl_UpdateTriggerCondition();
  // 	// 	NvM_MainFunction();
  // 	// 	Fee_MainFunction();
  // 	// 	Fls_17_Dmu_MainFunction();
  // 	//
  // NvM_GetErrorStatus(NvMConf___MultiBlockRequest,&RequestResultPtr);
  // 	// }while(RequestResultPtr == NVM_REQ_PENDING);

  //     /* save computed CRC to NVM if CRC success */
  //     FL_NvmInfo.blockInfo[index].blkChecksum =
  //         verifyParam.crcTotle;

  return (checkStatus == SECM_OK) ? FL_OK : FL_FAILED;
}

/******************************************************************************/
/**
 * @brief               <active job Hash check>
 * 脙鈥撁偮冣�∶偮懊冣�姑偮⒚冿拷脙鈥毭冿拷脙鈥姑偮睹兟犆冣�懊冣劉脙艩脙陆脗戮脙锟矫偮久冿拷脙锟矫偮Ｃ冣�樏兟┟偮睹兟犆冣�懊冣劉脙艩脙陆脗戮脙锟�
 *
 * <active checksum that request by 0x31 service> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
STATIC FL_ResultType FL_CheckSuming_Hash(uint8 index) {
  uint32 address;
  uint32 length;
  Std_ReturnType checkStatus;
  uint16 l_nrOfSegments;

  l_nrOfSegments = FldownloadStatus.segmentList[index].nrOfSegments;
  address = FldownloadStatus.segmentList[index]
                .segmentInfo[l_nrOfSegments - 1]
                .address;
  length = FldownloadStatus.segmentList[index]
               .segmentInfo[l_nrOfSegments - 1]
               .length;

  /* To-Do: when executing hash calculation, consider the P2* time-out situation
   */
  checkStatus = Secure_HashVerification(address, length, index);
  return (checkStatus == SECURE_PASS) ? FL_OK : FL_FAILED;
}
/******************************************************************************/
/**
 * @brief               <check programming integrity>
 *
 *
 *
 *
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
uint8 FL_CheckProgramIntegrity(void) {
  uint8 ret = NO_INTEGRITY_ERR;
  uint8 i = 0;

  if (FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkValid == FALSE) {
    ret |= APP_INTEGRITY_ERR;
  }

  //    if(FL_NvmInfo.blockInfo[CALDATABLOCKINDEX].blkValid==FALSE)
  //	{
  //		ret|=CAL_INTEGRITY_ERR;
  //	}
  //

  return ret;
}

/******************************************************************************/
/**
 * @brief               <check programming integrity>
 *
 *
 *
 *
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>
 */
/******************************************************************************/
#include "Os.h"
const uint8 Compatibility_Code[4] = {'V', '1', '.', '1'};
static FL_ResultType FL_CheckCompatibility(void) {
  FL_ResultType ret = FL_OK;
  // uint8 sourcedata[4],destdata[4];
  uint8 destdata[4];
  // FlashReadMemory(sourcedata,FBL_APP_CPB_FBL_ADDRESS,4);
  FlashReadMemory(destdata, FBL_APP_CPB_APP_ADDRESS, 4);
  appblkCpbDefault = FALSE;
  /*check project byte and cpbbyte*/
  // if((U8_FBL_APP_CPB_BYTE1==destdata[0])&&(U8_FBL_APP_CPB_BYTE3==destdata[2])&&(destdata[1]>=sourcedata[1])&&(destdata[3]>=sourcedata[3]))
  /* if(TRUE == FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkValid)
   {
       if((Compatibility_Code[0]==destdata[0])&&(Compatibility_Code[1]==destdata[1])&&(Compatibility_Code[2]==destdata[2])&&(Compatibility_Code[3]==destdata[3]))
       {
          FL_NvmInfo.isAppFblCpb=TRUE;
       }
       else
       {
       FL_NvmInfo.isAppFblCpb=FALSE;
       }
   }
   else
   {
          FL_NvmInfo.isAppFblCpb=FALSE;
   }*/
  FL_NvmInfo.isAppFblCpb = TRUE;
  FL_NvmInfo.isAppCalCpb = TRUE;
  ret = FL_UpdateNvm();

  return ret;
}

FL_ResultType FL_CheckProgramDependencies(uint8 *errorvalue) {
  FL_ResultType ret = FL_OK;
  *errorvalue = 00;
  //   if(FALSE==FL_NvmInfo.isAppCalCpb)
  //   {
  //	   *errorvalue|=CAL_APP_CPB_ERR ;
  //   }

  if (FALSE == FL_NvmInfo.isAppFblCpb) {
    *errorvalue |= FBL_APP_CPB_ERR;
  }

  return ret;
}

uint16 FL_CheckProgramCounter(void) {
  FL_ResultType ret = FL_OK;
  uint16 counter = 0;
  // counter=  FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkProgAttempt;
  counter = lastcounter;
  return counter;
}

FL_ResultType FL_CheckSWVerification(uint8 *checkdata) {
  FL_ResultType rtn = 0;
  int i;
  checkdata[0] = FL_NUM_LOGICAL_BLOCKS;

  for (i = 0; i < FL_NUM_LOGICAL_BLOCKS; i++) {
    checkdata[i * 5 + 1] = FL_BlkInfo[i].moduleid;
#if (CAL_CRC32 == CAL_METHOD)
    checkdata[i * 5 + 2] =
        (uint8)((FL_NvmInfo.blockInfo[i].blkChecksum & 0xFF000000) >> 24);
    checkdata[i * 5 + 3] =
        (uint8)((FL_NvmInfo.blockInfo[i].blkChecksum & 0x00FF0000) >> 16);
    checkdata[i * 5 + 4] =
        (uint8)((FL_NvmInfo.blockInfo[i].blkChecksum & 0x0000FF00) >> 8);
    checkdata[i * 5 + 5] =
        (uint8)(FL_NvmInfo.blockInfo[i].blkChecksum & 0x000000FF);

#else
    checkdata[i * 5 + 2] = 0x00;
    checkdata[i * 5 + 3] = 0x00;
    checkdata[i * 5 + 4] =
        (uint8)((FL_NvmInfo.blockInfo[i].blkChecksum & 0x0000FF00) >> 8);
    checkdata[i * 5 + 5] =
        (uint8)(FL_NvmInfo.blockInfo[i].blkChecksum & 0x000000FF);

#endif
  }
  return rtn;
}
FL_ResultType FL_SignVerifFlags(void) {
  FL_ResultType ret = FBL_FALSE;
  if (FL_HEADER_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep) {
    ret = FBL_TRUE;
  } else {
    ret = FBL_FALSE;
  }
  return ret;
}
void FL_SetRequestStep(void) {
  /* set the request step*/
  FldownloadStatus.downloadStep = FL_REQUEST_STEP;
  FldownloadStatus.activeJob = FL_JOB_IDLE;
}
void FL_SetChecksumStep(void) {
  /* set the checksum step*/
  FldownloadStatus.downloadStep = FL_CHECKSUM_STEP;
  FldownloadStatus.activeJob = FL_JOB_IDLE;
}
void UpdateSecurityErrorFlag(void) { EEP_SetSecErrFlag(&SecurityErrorFlag); }
uint8 FL_GetSecErrFlag(void) {
  FL_ResultType ret = FL_OK;
  uint8 flag = 0;
  flag = SecurityErrorFlag;
  return flag;
}

/*=======[E N D   O F   F I L E]==============================================*/
